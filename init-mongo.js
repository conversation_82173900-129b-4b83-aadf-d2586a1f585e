// MongoDB initialization script for Money Tracker
// This script creates the database and sets up initial collections

// Switch to the money_tracker database
db = db.getSiblingDB('money_tracker');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email', 'password'],
      properties: {
        username: {
          bsonType: 'string',
          description: 'Username must be a string and is required'
        },
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          description: 'Email must be a valid email address'
        },
        password: {
          bsonType: 'string',
          description: 'Password must be a string and is required'
        }
      }
    }
  }
});

db.createCollection('expenses');
db.createCollection('salaries');
db.createCollection('categories');
db.createCollection('budgets');

// Create indexes for better performance
db.users.createIndex({ 'username': 1 }, { unique: true });
db.users.createIndex({ 'email': 1 }, { unique: true });
db.expenses.createIndex({ 'user_id': 1, 'date': -1 });
db.salaries.createIndex({ 'user_id': 1, 'date': -1 });
db.categories.createIndex({ 'user_id': 1 });
db.budgets.createIndex({ 'user_id': 1 });

// Insert default categories
db.categories.insertMany([
  {
    name: 'Food & Dining',
    color: '#FF6B6B',
    icon: 'fas fa-utensils',
    user_id: null, // Global category
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Transportation',
    color: '#4ECDC4',
    icon: 'fas fa-car',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Shopping',
    color: '#45B7D1',
    icon: 'fas fa-shopping-bag',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Entertainment',
    color: '#96CEB4',
    icon: 'fas fa-film',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Bills & Utilities',
    color: '#FFEAA7',
    icon: 'fas fa-file-invoice-dollar',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Healthcare',
    color: '#DDA0DD',
    icon: 'fas fa-heartbeat',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Education',
    color: '#98D8C8',
    icon: 'fas fa-graduation-cap',
    user_id: null,
    is_default: true,
    created_at: new Date()
  },
  {
    name: 'Other',
    color: '#A8A8A8',
    icon: 'fas fa-ellipsis-h',
    user_id: null,
    is_default: true,
    created_at: new Date()
  }
]);

print('MongoDB initialization completed successfully!');
print('Created collections: users, expenses, salaries, categories, budgets');
print('Created indexes for better performance');
print('Inserted default categories');
