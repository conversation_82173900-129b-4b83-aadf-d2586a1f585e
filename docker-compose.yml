version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: moneytracker-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: money_tracker
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - moneytracker-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Flask Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: moneytracker-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - SECRET_KEY=dev-secret-key-change-in-production
      - MONGODB_URI=*****************************************/money_tracker?authSource=admin
      - DB_NAME=money_tracker
      - MAIL_SERVER=smtp.gmail.com
      - MAIL_PORT=587
      - MAIL_USE_TLS=True
      - MAIL_USERNAME=${MAIL_USERNAME:-}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-}
      - MAIL_DEFAULT_SENDER=${MAIL_DEFAULT_SENDER:-<EMAIL>}
      - SESSION_COOKIE_SECURE=False
      - LOG_LEVEL=DEBUG
    volumes:
      - ./logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - moneytracker-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: MongoDB Express for database management
  mongo-express:
    image: mongo-express:1.0.0
    container_name: moneytracker-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - moneytracker-network

volumes:
  mongodb_data:
    driver: local

networks:
  moneytracker-network:
    driver: bridge
