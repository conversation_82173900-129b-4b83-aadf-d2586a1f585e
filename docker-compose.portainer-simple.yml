version: '3.8'

services:
  # Money Tracker Flask Application
  moneytracker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: moneytracker-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      # Flask Configuration
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      
      # Security - <PERSON><PERSON><PERSON> THIS IN PRODUCTION!
      - SECRET_KEY=${SECRET_KEY}
      
      # Database Configuration
      - MONGODB_URI=${MONGODB_URI}
      - DB_NAME=${DB_NAME:-moneytracker}
      
      # Email Configuration (for password reset)
      - MAIL_SERVER=${MAIL_SERVER:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USE_TLS=${MAIL_USE_TLS:-True}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - MAIL_DEFAULT_SENDER=${MAIL_DEFAULT_SENDER:-<EMAIL>}
      
      # Security Settings
      - SESSION_COOKIE_SECURE=${SESSION_COOKIE_SECURE:-False}
      
      # Application Settings
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TZ=${TZ:-UTC}
    
    volumes:
      # Mount logs directory for persistence
      - moneytracker_logs:/app/logs
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits (adjust based on your server capacity)
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

volumes:
  moneytracker_logs:
    driver: local
