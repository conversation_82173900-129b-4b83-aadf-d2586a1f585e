version: '3.8'

services:
  # Money Tracker Flask Application
  moneytracker:
    image: nikharmsingh/moneytracker:latest  # Replace with your actual registry/image
    container_name: moneytracker-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      # Flask Configuration
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      
      # Security - CHANGE THIS IN PRODUCTION!
      - SECRET_KEY=${SECRET_KEY}
      
      # Database Configuration - Use your MongoDB Atlas or external MongoDB
      - MONGODB_URI=${MONGODB_URI}
      - DB_NAME=${DB_NAME:-money_tracker}
      
      # Email Configuration (for password reset)
      - MAIL_SERVER=${MAIL_SERVER:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_USE_TLS=${MAIL_USE_TLS:-True}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - MAIL_DEFAULT_SENDER=${MAIL_DEFAULT_SENDER:-<EMAIL>}
      
      # Security Settings
      - SESSION_COOKIE_SECURE=${SESSION_COOKIE_SECURE:-True}
      
      # Application Settings
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TZ=${TZ:-UTC}
      
      # Gunicorn Settings
      - WEB_CONCURRENCY=${WEB_CONCURRENCY:-4}
      - TIMEOUT=${TIMEOUT:-120}
      - KEEP_ALIVE=${KEEP_ALIVE:-2}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - MAX_REQUESTS_JITTER=${MAX_REQUESTS_JITTER:-100}
    
    volumes:
      # Mount logs directory for persistence
      - moneytracker_logs:/app/logs
    
    networks:
      - moneytracker-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits (adjust based on your server capacity)
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # Optional: Nginx reverse proxy for HTTPS and better performance
  nginx:
    image: nginx:alpine
    container_name: moneytracker-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # Mount your SSL certificates here
      - nginx_logs:/var/log/nginx
    depends_on:
      - moneytracker
    networks:
      - moneytracker-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M

volumes:
  moneytracker_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  moneytracker-network:
    driver: bridge

# Environment variables that need to be set in Portainer:
# 
# Required:
# SECRET_KEY=your-super-secret-key-here
# MONGODB_URI=mongodb+srv://username:<EMAIL>/money_tracker?retryWrites=true&w=majority
# 
# Optional Email Configuration:
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_DEFAULT_SENDER=<EMAIL>
# 
# Optional Settings:
# DB_NAME=money_tracker
# LOG_LEVEL=INFO
# TZ=UTC
# SESSION_COOKIE_SECURE=True
# WEB_CONCURRENCY=4
