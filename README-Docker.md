# Money Tracker - Docker Deployment Guide

This guide covers how to deploy the Money Tracker application using Docker and Portainer.

## 📋 Prerequisites

- <PERSON>er and Docker Compose installed
- MongoDB Atlas account (recommended) or local MongoDB instance
- Portainer.io setup (for Portainer deployment)
- Gmail account with App Password (for email functionality)

## 🚀 Quick Start - Local Development

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd moneytracker
```

### 2. Configure Environment

Copy the environment template and configure it:

```bash
cp .env.docker .env
```

Edit `.env` with your configuration:
- Set a secure `SECRET_KEY`
- Configure `MONGODB_URI` (MongoDB Atlas recommended)
- Set up email credentials for password reset functionality

### 3. Run with Docker Compose

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

The application will be available at:
- **Money Tracker**: http://localhost:5000
- **MongoDB Express**: http://localhost:8081 (admin/admin123)

## 🌐 Production Deployment with Portainer

### Step 1: Prepare Your Image

#### Option A: Build Locally and Push to Registry

```bash
# Build the image
docker build -t your-registry/moneytracker:latest .

# Push to your registry (Docker Hub, GitHub Container Registry, etc.)
docker push your-registry/moneytracker:latest
```

#### Option B: Use GitHub Actions (Recommended)

Create `.github/workflows/docker-build.yml`:

```yaml
name: Build and Push Docker Image

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: ghcr.io/${{ github.repository }}:latest
```

### Step 2: Setup MongoDB Atlas

1. Create a MongoDB Atlas account at https://www.mongodb.com/cloud/atlas
2. Create a new cluster
3. Create a database user with read/write permissions
4. Get your connection string
5. Whitelist your server's IP address (or use 0.0.0.0/0 for any IP)

### Step 3: Configure Gmail for Email

1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. Use this App Password in your environment variables

### Step 4: Deploy in Portainer

1. **Login to Portainer**
2. **Go to Stacks → Add Stack**
3. **Name your stack**: `moneytracker`
4. **Upload or paste** the `docker-compose.portainer.yml` content
5. **Configure Environment Variables**:

```env
SECRET_KEY=your-super-secret-key-here-make-it-long-and-random
MONGODB_URI=mongodb+srv://username:<EMAIL>/money_tracker?retryWrites=true&w=majority
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-character-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
DB_NAME=money_tracker
LOG_LEVEL=INFO
TZ=UTC
SESSION_COOKIE_SECURE=true
```

6. **Deploy the stack**

### Step 5: Setup Reverse Proxy (Optional but Recommended)

If you want to use the included Nginx reverse proxy:

1. **Generate SSL certificates** (Let's Encrypt recommended):
```bash
# Using certbot
sudo certbot certonly --standalone -d yourdomain.com
```

2. **Copy certificates** to your server and mount them in the nginx service

3. **Update nginx.conf** with your domain name

4. **Uncomment SSL configuration** in nginx.conf

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SECRET_KEY` | Flask secret key for sessions | - | ✅ |
| `MONGODB_URI` | MongoDB connection string | - | ✅ |
| `DB_NAME` | Database name | `money_tracker` | ❌ |
| `MAIL_USERNAME` | Gmail address | - | ❌ |
| `MAIL_PASSWORD` | Gmail App Password | - | ❌ |
| `MAIL_DEFAULT_SENDER` | Email sender address | `<EMAIL>` | ❌ |
| `LOG_LEVEL` | Logging level | `INFO` | ❌ |
| `TZ` | Timezone | `UTC` | ❌ |
| `SESSION_COOKIE_SECURE` | HTTPS-only cookies | `True` | ❌ |

### Resource Requirements

**Minimum:**
- CPU: 0.5 cores
- RAM: 256MB
- Storage: 1GB

**Recommended:**
- CPU: 1 core
- RAM: 512MB
- Storage: 5GB

## 🔍 Monitoring and Maintenance

### Health Checks

The application includes built-in health checks:
- **Application**: `http://your-domain:5000/`
- **Docker health check**: Automatically configured

### Logs

View application logs:
```bash
# In Portainer: Go to Containers → moneytracker-app → Logs
# Or via Docker CLI:
docker logs moneytracker-app -f
```

### Backup

**Database Backup** (if using local MongoDB):
```bash
docker exec moneytracker-mongodb mongodump --out /backup
```

**Application Data**:
- Logs are persisted in Docker volumes
- User uploads (if any) should be backed up

### Updates

1. **Pull new image**:
```bash
docker pull your-registry/moneytracker:latest
```

2. **Update stack in Portainer** or restart services:
```bash
docker-compose pull
docker-compose up -d
```

## 🛠️ Troubleshooting

### Common Issues

1. **Application won't start**:
   - Check environment variables
   - Verify MongoDB connection
   - Check logs for specific errors

2. **Database connection failed**:
   - Verify MongoDB URI
   - Check network connectivity
   - Ensure MongoDB Atlas IP whitelist includes your server

3. **Email not working**:
   - Verify Gmail App Password
   - Check MAIL_* environment variables
   - Ensure 2FA is enabled on Gmail account

4. **SSL/HTTPS issues**:
   - Verify certificate paths
   - Check nginx configuration
   - Ensure certificates are valid

### Getting Help

1. Check application logs
2. Verify all environment variables are set correctly
3. Test MongoDB connection separately
4. Check Portainer container status

## 🔐 Security Considerations

1. **Use strong SECRET_KEY**: Generate a random 32+ character string
2. **Enable HTTPS**: Use SSL certificates in production
3. **Secure MongoDB**: Use MongoDB Atlas or secure your MongoDB instance
4. **Regular Updates**: Keep Docker images and dependencies updated
5. **Backup Strategy**: Implement regular database backups
6. **Monitor Logs**: Set up log monitoring for security events

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Portainer Documentation](https://docs.portainer.io/)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [Flask Security Best Practices](https://flask.palletsprojects.com/en/2.3.x/security/)

---

For more information about the Money Tracker application, see the main [README.md](README.md) file.
