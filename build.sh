#!/bin/bash

# Money Tracker Docker Build Script
# This script helps build and manage the Docker image for Money Tracker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="moneytracker"
TAG="latest"
REGISTRY=""
PUSH=false
BUILD_ONLY=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME       Image name (default: moneytracker)"
    echo "  -t, --tag TAG         Image tag (default: latest)"
    echo "  -r, --registry REG    Registry to push to (e.g., ghcr.io/username)"
    echo "  -p, --push            Push image to registry after building"
    echo "  -b, --build-only      Only build, don't run any containers"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Build locally"
    echo "  $0 -p -r ghcr.io/username            # Build and push to GitHub Container Registry"
    echo "  $0 -t v1.0.0 -p -r docker.io/user   # Build with specific tag and push to Docker Hub"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Construct full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
fi

print_status "Starting build process..."
print_status "Image name: $FULL_IMAGE_NAME"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile not found in current directory."
    exit 1
fi

# Build the image
print_status "Building Docker image..."
if docker build -t "$FULL_IMAGE_NAME" .; then
    print_success "Docker image built successfully: $FULL_IMAGE_NAME"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Push to registry if requested
if [ "$PUSH" = true ]; then
    if [ -z "$REGISTRY" ]; then
        print_error "Registry not specified. Use -r option to specify registry."
        exit 1
    fi
    
    print_status "Pushing image to registry..."
    if docker push "$FULL_IMAGE_NAME"; then
        print_success "Image pushed successfully to registry"
    else
        print_error "Failed to push image to registry"
        exit 1
    fi
fi

# If not build-only, offer to run the container
if [ "$BUILD_ONLY" = false ]; then
    echo ""
    print_status "Build completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. For local development:"
    echo "   docker-compose up -d"
    echo ""
    echo "2. For production deployment:"
    echo "   - Update docker-compose.portainer.yml with your image name"
    echo "   - Deploy using Portainer or docker-compose"
    echo ""
    echo "3. To run a single container:"
    echo "   docker run -d -p 5000:5000 --env-file .env $FULL_IMAGE_NAME"
fi

print_success "All done! 🚀"
