#!/bin/bash

# Reset Money Tracker App - Clear all sessions and restart containers

echo "🔄 Resetting Money Tracker Application..."

# Stop containers
echo "⏹️  Stopping containers..."
docker compose down

# Remove any orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker compose down --remove-orphans

# Rebuild the application image
echo "🔨 Rebuilding application image..."
docker build -t moneytracker:latest .

# Start containers
echo "🚀 Starting containers..."
docker compose up -d

# Wait a moment for containers to start
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo "📊 Container status:"
docker compose ps

# Show logs
echo "📋 Recent logs:"
docker compose logs --tail=20

echo ""
echo "✅ Reset complete!"
echo ""
echo "🌐 Application should be available at: http://localhost:5000"
echo "🗄️  MongoDB Express (database admin): http://localhost:8082"
echo "🔧 If you still see session errors, visit: http://localhost:5000/clear-session"
echo ""
echo "💡 To clear browser data:"
echo "   1. Open browser developer tools (F12)"
echo "   2. Go to Application/Storage tab"
echo "   3. Clear all cookies and local storage for localhost:5000"
echo "   4. Refresh the page"
